package main

import rl "vendor:raylib"

main :: proc() {
    // Initialize the window
    screen_width: i32 = 800
    screen_height: i32 = 600
    
    rl.InitWindow(screen_width, screen_height, "Simple Odin Window")
    defer rl.CloseWindow()
    
    // Set target FPS
    rl.SetTargetFPS(60)
    
    // Main game loop
    for !rl.WindowShouldClose() {
        // Update
        // (Add any update logic here)
        
        // Draw
        rl.BeginDrawing()
        defer rl.EndDrawing()
        
        rl.ClearBackground(rl.RAYWHITE)
        
        // Draw some text
        rl.DrawText("Hello, Odin Window!", 190, 200, 20, rl.LIGHTGRAY)
        rl.DrawText("Press ESC to close", 290, 240, 20, rl.GRAY)
        
        // Draw a simple rectangle
        rl.DrawRectangle(300, 300, 200, 100, rl.SKYBLUE)
        rl.DrawRectangleLines(300, 300, 200, 100, rl.BLUE)
        
        // Draw a circle
        rl.DrawCircle(400, 150, 50, rl.MAROON)
    }
}
